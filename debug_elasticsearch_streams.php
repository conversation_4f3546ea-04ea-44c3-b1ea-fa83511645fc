<?php
/**
 * Debug script to check what stream data is available in Elasticsearch
 */

require_once __DIR__ . '/vendor/autoload.php';

// Initialize Yii application
$config = require __DIR__ . '/console/config/main.php';
new yii\console\Application($config);

use common\services\CollegeService;
use frontend\models\CollegeSearch;

echo "Debugging Elasticsearch stream data...\n\n";

$collegeService = new CollegeService();
$searchModel = new CollegeSearch();

try {
    // Get debug data from Elasticsearch
    $debugData = $collegeService->debugElasticsearchStreamData($searchModel);
    
    echo "=== ELASTICSEARCH DEBUG DATA ===\n";
    echo "Total colleges in ES: " . $debugData['total_colleges'] . "\n\n";
    
    echo "Available Stream Names:\n";
    echo str_repeat("-", 40) . "\n";
    foreach ($debugData['stream_names'] as $streamName => $count) {
        echo sprintf("%-30s: %d\n", $streamName, $count);
    }
    
    echo "\nAvailable College Types:\n";
    echo str_repeat("-", 40) . "\n";
    foreach ($debugData['college_types'] as $type => $count) {
        echo sprintf("%-30s: %d\n", $type, $count);
    }
    
    echo "\n=== TESTING STREAM COUNTS ===\n";
    $streamMap = [
        11 => 'Engineering',
        14 => 'Management',
        6  => 'Commerce',
        16  => 'Medical',
        13 => 'Law'
    ];
    
    // Test the stream counts method
    $streamCounts = $collegeService->getElasticsearchAggregationData($searchModel);
    
    echo "Stream counts from optimized method:\n";
    echo str_repeat("-", 50) . "\n";
    foreach ($streamMap as $id => $name) {
        $publicCount = $streamCounts['streamCounts'][$id]['public_count'] ?? 0;
        $privateCount = $streamCounts['streamCounts'][$id]['private_count'] ?? 0;
        $total = $publicCount + $privateCount;
        
        echo sprintf("%-15s (ID: %2d): Public: %4d, Private: %4d, Total: %4d\n", 
            $name, $id, $publicCount, $privateCount, $total);
    }
    
    echo "\n=== COMPARISON WITH DATABASE METHOD ===\n";
    
    // Compare with database method
    $dbStats = $collegeService->getCollegeStatistics();
    
    echo "Database method stream counts:\n";
    echo str_repeat("-", 50) . "\n";
    foreach ($streamMap as $id => $name) {
        $count = $dbStats['stream_wise_colleges'][$name] ?? 0;
        echo sprintf("%-15s (ID: %2d): %4d colleges\n", $name, $id, $count);
    }
    
    echo "\n=== RECOMMENDATIONS ===\n";
    
    $esTotal = array_sum(array_column($streamCounts['streamCounts'], 'public_count')) + 
               array_sum(array_column($streamCounts['streamCounts'], 'private_count'));
    $dbTotal = array_sum($dbStats['stream_wise_colleges']);
    
    echo "Elasticsearch total stream colleges: $esTotal\n";
    echo "Database total stream colleges: $dbTotal\n";
    
    if ($esTotal < $dbTotal * 0.5) {
        echo "\n⚠️  WARNING: Elasticsearch counts are significantly lower than database counts.\n";
        echo "This suggests the Elasticsearch query needs to be improved or the data mapping is different.\n";
        echo "The fallback to database method will be used.\n";
    } else {
        echo "\n✅ Elasticsearch counts look reasonable compared to database counts.\n";
    }
    
} catch (Exception $e) {
    echo "Error during debugging: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
