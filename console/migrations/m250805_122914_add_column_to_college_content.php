<?php

use yii\db\Migration;

/**
 * Class m250805_122914_add_column_to_college_content
 */
class m250805_122914_add_column_to_college_content extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('college_content', 'content_updated_at', $this->dateTime()->defaultValue(null));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('{{%college_content}}', 'content_updated_at');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250805_122914_add_column_to_college_content cannot be reverted.\n";

        return false;
    }
    */
}
