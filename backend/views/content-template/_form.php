<?php

use common\models\ContentTemplate;
use common\models\Exam;
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use common\helpers\DataHelper;
use kartik\depdrop\DepDrop;
use kartik\select2\Select2;
use unclead\multipleinput\MultipleInput;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;
use yii\web\JsExpression;

/* @var $this yii\web\View */
/* @var $model common\models\ContentTemplate */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="content-template-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>

    <div class="box-body table-responsive">
        <?= $form->field($model, 'entity_type')->dropDownList(DataHelper::$entitiesTemplate, [
            'id' => 'entity_type',
            'placeholder' => 'Select..',
            'disabled' => !$model->isNewRecord,
            'onchange' => 'handleEntityTypeChange()'
        ])->label('Entity Type') ?>

        <?= $form->field($model, 'name')->textInput(['maxlength' => true]) ?>

        <?= $form->field($model, 'entity_id')->widget(Select2::classname(), [
            'data' => !empty($model->entity_id) ? ArrayHelper::map(Exam::find()->where(['id' => $model->entity_id])->all(), 'id', 'name') : [],
            'language' => 'en',
            'options' => [
                'placeholder' => '--Select--',
                'multiple' => false,
            ],
            'pluginOptions' => [
                'allowClear' => true,
                'placeholder' => '--Select--',
                'disabled' => !$model->isNewRecord,
                'minimumInputLength' => 3,
                'language' => [
                    'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                ],
                'ajax' => [
                    'url' => Url::to(['/faq/get-list']),
                    'dataType' => 'json',
                    'data' => new JsExpression("function(params) {return {q:params.term,depdrop_parents:$('#entity_type').val()}; }")
                ],
                'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                'templateResult' => new JsExpression('function(data) { return data.text; }'),
                'templateSelection' => new JsExpression('function (data) { return data.text; }'),
            ],
        ])->label('Entity Name');
?>

        <?= $form->field($model, 'page')->widget(DepDrop::class, [
            'type' => DepDrop::TYPE_SELECT2,
            'options' => [
                'id' => 'page',
                'placeholder' => '--Select--',
                'multiple' => false,
            ],
            'data' => !empty($model->page) ? [$model->page] : [],
            'select2Options' => ['pluginOptions' => [
                'allowClear' => true,
                'disabled' => !$model->isNewRecord ? true : false
            ]],
            'pluginOptions' => [
                'depends' => ['entity_type'],
                'placeholder' => 'Select...',
                'url' => Url::to(['/content-template/get-page'])
            ],
        ])->label('Page');
?>
        <?=
        $this->render('/widget/tinymce', [
            'form' => $form,
            'model' => $model,
            'entity' => 'content',
            'type' => 'content_template'
        ])
        ?>

        <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', ContentTemplate::class)); ?>


    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>

<script>
    function handleEntityTypeChange() {
        var entityType = document.getElementById('entity_type').value.toLowerCase();
        if (entityType == "exam") {
            $("#contenttemplate-name").attr("disabled", true).val("");
            $(".field-contenttemplate-name").hide();
            $("#contenttemplate-entity_id").attr("disabled", false);
            $(".field-contenttemplate-entity_id").show();
        } else if (entityType == "filter") {
            $("#page").attr("disabled", true);
        } else if (entityType == "all_listing") {
            $("#contenttemplate-entity_id option").remove();
            $("#contenttemplate-entity_id").attr("disabled", true);
            $(".field-contenttemplate-entity_id").hide();
            $(".field-contenttemplate-name").show();
            $("#contenttemplate-name").attr("disabled", false);
            // Set default page value for all listing
        } else {
            if (entityType == "college") {
                $("#contenttemplate-entity_id option").remove();
                $("#contenttemplate-entity_id").attr("disabled", true);
                $(".field-contenttemplate-entity_id").hide();
                $(".field-contenttemplate-name").show();
                $("#contenttemplate-name").attr("disabled", false);
            }
        }
    }

    // Call the function on page load
    document.addEventListener('DOMContentLoaded', function() {
        handleEntityTypeChange();
    });
</script>