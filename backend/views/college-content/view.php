<?php

use common\helpers\DataHelper;
use common\models\College;
use common\models\CollegeContent;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\CollegeContent */

$this->title = 'College Content Update: ' . ($model->entityName->name ?? $model->entityName->display_name) ?? $model->id;
$this->params['breadcrumbs'][] = ['label' => 'College Contents', 'url' => ['index']];
$this->params['breadcrumbs'][] = ($model->entityName->name ?? $model->entityName->display_name) ?? $model->id;
?>
<div class="college-content-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?= Html::a('Preview', ['preview', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat', 'target' => '_blank']) ?>
        <?= Html::a('Index', ['college-content/index', 'CollegeContentSearch[college_slug]' => $model->college->slug], ['class' => 'btn btn-primary btn-flat']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                'id',
                // 'category_id',
                'author.username',
                'entity',
                [
                    'attribute' => 'entity_id',
                    'value' => function ($model) {

                        return $model->entityName ? $model->entityName->name : '';
                    }
                ],
                [
                    'label' => 'Content',
                    'format' => 'raw',
                    'value' => html_entity_decode($model->content),
                ],
                [
                    'label' => 'Status',
                    'attribute' => 'status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', CollegeContent::class), $model->status)
                ],
                'created_at:datetime',
               // 'updated_at:datetime',
                [
                    'attribute' => 'updated_at',
                    'label' => 'Last Updated',
                    'format' => 'raw',
                    'value' => function ($model) {
                        $date = !empty($model->content_updated_at)
                            ? $model->content_updated_at
                            : $model->updated_at;
        
                        return Yii::$app->formatter->asDatetime($date);
                    },
                ],
        
            ],
        ]) ?>
    </div>
</div>


<?php if ($model->sub_page == 'cut-off'): ?>
    <!-- render cutoff h1 detail tabulat form -->
    <?php if (!empty($courseIds)):
        echo $this->render('/cutoff-detail-h1-description/_tabular_form', [
            'cutoffModels' => $cutOffModels ?? [],
            'college_id' => $college_id ?? '',
            'courseIds' => $courseIds ?? [],
            'isSingleProgram' => $isSingleProgram
        ]);
    endif;
endif; ?>