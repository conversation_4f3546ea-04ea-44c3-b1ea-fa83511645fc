<?php

namespace backend\controllers;

use Yii;
use common\models\ContentTemplate;
use common\models\ContentTemplateSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use common\helpers\CollegeHelper;

/**
 * ContentTemplateController implements the CRUD actions for ContentTemplate model.
 */
class ContentTemplateController extends Controller
{
    /**
     * Lists all ContentTemplate models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new ContentTemplateSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single ContentTemplate model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new ContentTemplate model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new ContentTemplate();

        if ($model->load(Yii::$app->request->post())) {
            if ($model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            } else {
                Yii::$app->session->setFlash('error', 'Entity Type and Subpage Combiantion already exists');
                return $this->redirect(['index']);
            }
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing ContentTemplate model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing ContentTemplate model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the ContentTemplate model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return ContentTemplate the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = ContentTemplate::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function actionGetPage()
    {
        $collegePage = CollegeHelper::$subPages;
        $collegePage['pi'] = 'College Program Page';
        $allListingPage = [
            'all-colleges' => 'All Colleges'
        ];
        $entitySubPageArr = [
            'college' => $collegePage,
            'exam' => $collegePage,
            'all_listing' => $allListingPage
        ];

        $requestParam = Yii::$app->request->post('depdrop_parents');

        if (!empty($requestParam)) {
            $entity = $requestParam[0];

            $subpage = $entitySubPageArr[$entity];
            $subpage = array_map(function ($key, $value) {
                return [
                    'id' => $key,
                    'name' => $value
                ];
            }, array_keys($subpage), array_values($subpage));

            // Set selected default based on entity
            $selected = null;
            if ($entity === 'all_listing') {
                $selected = 'all-colleges';
            } elseif ($entity === 'college') {
                // optionally set other defaults here
                $selected = '';
            }
            return json_encode(['output' => $subpage, 'selected' => $selected]);
        }
    }
}
