<?php

namespace backend\controllers;

use Yii;
use common\models\CollegeContent;
use backend\models\CollegeContentSearch;
use common\helpers\CollegeHelper;
use common\models\College;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\helpers\ArrayHelper;
use common\models\Course;
use common\models\Exam;
use common\helpers\DataHelper;
use common\models\Article;
use common\models\CutOff;
use common\models\CutoffDetailH1Description;
use common\models\News;
use common\services\CollegeService;
use Error;
use Exception;
use yii\base\Model;
use yii\db\Query;

/**
 * CollegeContentController implements the CRUD actions for CollegeContent model.
 */
class CollegeContentController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all CollegeContent models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new CollegeContentSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single CollegeContent model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        $collegeContentModel = $this->findModel($id);

        if (empty($cutOffModels)) {
            $newModel = new CutoffDetailH1Description();
            $newModel->college_id = $collegeContentModel->entity_id;
            $cutOffModels[] = $newModel;
        }

        $courseIds = CutOff::find()->select(['course_id'])->where(['college_id' => $collegeContentModel->entity_id])->distinct()->column();

        $existingModels = CutoffDetailH1Description::find()
            ->where(['college_id' => $collegeContentModel->entity_id])
            ->andWhere(['course_id' => $courseIds])
            ->indexBy('course_id')
            ->all();

        $cutOffModels = [];

        foreach ($courseIds as $courseId) {
            $programCount = CutOff::find()
                ->select(['program_name'])
                ->distinct()
                ->where([
                    'college_id' => $collegeContentModel->entity_id,
                    'course_id' => $courseId
                ])
                ->distinct()
                ->count();

            $isSingleProgram = $programCount === '1' ? true : false;

            if (isset($existingModels[$courseId])) {
                $cutOffModels[] = $existingModels[$courseId];
            } else {
                $cutOffModels[] = new CutoffDetailH1Description([
                    'college_id' => $collegeContentModel->entity_id,
                    'course_id' => $courseId,
                    'h1' => DataHelper::$cutOffH1DesDefaultValue['h1'],
                    'description' => $isSingleProgram ? DataHelper::$cutOffH1DesDefaultValue['single_desc'] :
                        DataHelper::$cutOffH1DesDefaultValue['multi_desc'],
                    'status' => 1,
                ]);
            }
        }

        // Handle form post
        if (Yii::$app->request->isPost && Yii::$app->request->post('CutoffDetailH1Description')) {
            $postData = Yii::$app->request->post('CutoffDetailH1Description');
            $models = [];

            foreach ($postData as $row) {
                $model = isset($row['id']) && !empty($row['id'])
                    ? CutoffDetailH1Description::findOne($row['id'])
                    : new CutoffDetailH1Description();

                $model->college_id = $collegeContentModel->entity_id;
                $models[] = $model;
            }

            if (Model::loadMultiple($models, Yii::$app->request->post()) && Model::validateMultiple($models)) {
                foreach ($models as $model) {
                    if (!$model->save()) {
                        Yii::$app->session->setFlash('error', 'Error saving: ' . json_encode($model->getErrors()));
                    }
                }
                Yii::$app->session->setFlash('success', 'Cutoff H1 details saved successfully.');
                return $this->redirect(['view', 'id' => $id]);
            }
        }

        return $this->render('view', [
            'model' => $collegeContentModel,
            'cutOffModels' => $cutOffModels,
            'college_id' => $collegeContentModel->entity_id,
            'courseIds' => $courseIds,
            'isSingleProgram' => $isSingleProgram ?? ''
        ]);
    }

    /**
     * Creates a new CollegeContent model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new CollegeContent();
        if ($model->load(Yii::$app->request->post())) {
            $data = Yii::$app->request->post();

            if ($data['CollegeContent']['sub_page'] == 'news' && !CollegeService::isArticlesNewsAllowed($data['CollegeContent']['entity_id'])) {
                $model->addError('sub_page', 'Selected Entity does not have enough Articles/News to be published');
                return $this->render('create', [
                    'model' => $model
                ]);
            }

            /**** Check for cache and restricted urls in content ****/
            if (!empty($data['CollegeContent']['content'])) {
                $restrictedUrl = DataHelper::checkRestrictedUrl($data['CollegeContent']['content']);
            }
            if (!empty($restrictedUrl)) {
                return $this->redirect(Yii::$app->request->referrer);
            }
            /*********************************************************/

            if (!($data['CollegeContent']['parent_id'])) {
                $checkISExist = CollegeContent::find()->where(['=', 'entity', $data['CollegeContent']['entity']])
                    ->andWhere(['=', 'entity', $data['CollegeContent']['entity']])
                    ->andWhere(['=', 'entity_id', $data['CollegeContent']['entity_id']])
                    ->andWhere(['=', 'sub_page', $data['CollegeContent']['sub_page']])
                    ->count();
                if ($checkISExist != 0) {
                    $model->addError('entity_id', 'This Entity and Entity Page has already been taken');
                    return $this->render('create', [
                        'model' => $model
                    ]);
                }
            }

            if (!empty($data['CollegeContent']['parent_id'])) {
                $existingContent = $this->checkExistingContent($data);
                if ($existingContent->id) {
                    $dropDownValue = '';
                    if ($data['CollegeContent']['sub_page'] == 'syllabus') {
                        $dropDownValue = $this->getCourseSlug($data['CollegeContent']['parent_id']);
                    } elseif ($data['CollegeContent']['sub_page'] == 'cut-off') {
                        $dropDownValue = $dropDownValue = $this->getExamSlug($data['CollegeContent']['parent_id']);
                    } elseif ($data['CollegeContent']['sub_page'] == 'admission') {
                        $type = ($model->parent_id == 1) ? 'UG' : 'PG';
                        $dropDownValue = $type;
                    }

                    $existingSubPageContent = $this->checkExistingSubPageContent($data, $dropDownValue);
                    if (empty($existingSubPageContent)) {
                        $model->parent_id = $existingContent->id;
                        $model->sub_page = $dropDownValue;
                    } else {
                        Yii::$app->session->setFlash('error', 'College and Sub Page combination already exist');
                        return $this->redirect('/college-content/index');
                    }
                } else {
                    Yii::$app->session->setFlash('error', 'College and Page combination does not exist');
                    return $this->redirect('/college-content/index');
                }
            } else {
                $model->parent_id = null;
            }
            $model->content_updated_at = date('Y-m-d h:i:s');
            if ($model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            }

            return $this->render('create', [
                'model' => $model
            ]);
        } else {
            return $this->render('create', [
                'model' => $model
            ]);
        }
    }

    /**
     * Updates an existing CollegeContent model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post())) {
            $data = Yii::$app->request->post();

            /**** Check for cache and restricted urls in content ****/
            if (!empty($data['CollegeContent']['content'])) {
                $restrictedUrl = DataHelper::checkRestrictedUrl($data['CollegeContent']['content']);
            }
            if (!empty($restrictedUrl)) {
                return $this->redirect(Yii::$app->request->referrer);
            }
            /********************************************************/
            $model->content_updated_at = date('Y-m-d h:i:s');
            if ($model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            }
            return $this->render('update', [
                'model' => $model
            ]);
        } else {
            if (!empty($model->parent_id)) {
                $parentModel = $this->findModel($model->parent_id);
                if ($parentModel->sub_page == 'syllabus') {
                    $course = Course::find()->select(['id', 'short_name', 'slug'])->where(['slug' => $model->sub_page])->one();
                    $model->sub_page = $parentModel->sub_page . '||' . $course->short_name;
                } elseif ($parentModel->sub_page == 'cut-off') {
                    $exam = Exam::find()->select(['id', 'slug', 'display_name'])->where(['slug' => $model->sub_page])->one();
                    $model->sub_page = $parentModel->sub_page . '||' . $exam->display_name;
                } else {
                    $model->sub_page = $parentModel->sub_page . '||' . $model->sub_page;
                }
                $model->parent_id = 1;
            }
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing CollegeContent model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the CollegeContent model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return CollegeContent the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = CollegeContent::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    /**
     * Finds college list on the basis of sarch parameter.
     * @param string $q
     * @return College List matched with $q param
     */
    public function actionCollegeList($q)
    {
        \Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $data = College::find()->select(['id', 'name'])->where(['like', 'name', $q])->all();
        $out['results'] = array_values($data);
        return $out;
    }

    public function actionCheckCollegePageCombination()
    {
        $data = Yii::$app->request->post();
        $allowed = ['syllabus', 'cut-off', 'admission'];
        if (in_array($data['page'], $allowed)) {
            $existingContent = CollegeContent::find()->select('id')->where(['entity_id' => $data['college']])->andWhere(['sub_page' => $data['page']])->one();
            if (!empty($existingContent) && $existingContent->id) {
                return true;
            }
        }
        return false;
    }

    //to do
    public function actionGetSubPage()
    {
        $subPageArr = [];
        $requestParam = Yii::$app->request->post('depdrop_parents');

        if (!empty($requestParam)) {
            if ($requestParam['0'] == 'syllabus') {
                $data = Course::find()->select(['id', 'short_name'])->parentOnly()->andWhere(['status' => 1])->asArray()->all();
                foreach ($data as $key => $value) {
                    $subPageArr[$value['id']] = $value['short_name'];
                }
            } elseif ($requestParam['0'] == 'cut-off') {
                $data = Exam::find()
                    ->select(['id', 'display_name'])
                    ->andWhere(['status' => 1])->asArray()
                    ->all();
                foreach ($data as $key => $value) {
                    $subPageArr[$value['id']] = $value['display_name'];
                }
            } elseif ($requestParam['0'] == 'admission') {
                $subPageArr = [
                    '1' => 'UG',
                    '2' => 'PG'
                ];
            }

            $subpage = array_map(function ($key, $value) {
                return [
                    'id' => $key,
                    'name' => $value
                ];
            }, array_keys($subPageArr), array_values($subPageArr));

            return json_encode(['output' => $subpage, 'selected' => '']);
        }
    }

    protected function checkExistingContent($data)
    {
        return CollegeContent::find()->select('id')->where(['entity_id' => $data['CollegeContent']['entity_id']])->andWhere(['sub_page' => $data['CollegeContent']['sub_page']])->one();
    }

    protected function getCourseSlug($parent_id)
    {
        $course =  Course::find()->select(['id', 'short_name', 'slug'])->where(['id' => $parent_id])->one();
        return $course->slug;
    }

    protected function getExamSlug($parent_id)
    {
        $exam = Exam::find()->select(['id', 'slug'])->where(['id' => $parent_id])->one();
        return $exam->slug;
    }

    protected function checkExistingSubPageContent($data, $dropDownValue)
    {
        return CollegeContent::find()->select('id')->where(['entity_id' => $data['CollegeContent']['entity_id']])->andWhere(['sub_page' => $dropDownValue])->one();
    }

    /**
     * Displays a single CollegeContent value for preview.
     * @param integer $id
     * @return mixed
     */
    public function actionPreview($id)
    {
        return $this->render('preview', [
            'model' => $this->findModel($id),
        ]);
    }

    public function actionAllActiveSubpage()
    {

        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        $requestParam = Yii::$app->request->post('depdrop_parents');
        $collegeID = $requestParam[0] ?? 0;
        $subPageArray = [];
        if ($collegeID) {
            $existingContent = CollegeContent::find()->select(['sub_page'])->where(['entity_id' => $collegeID])
                ->andWhere(['parent_id' => null])
                ->andWhere(['entity' => 'college'])
                ->andWhere(['not', ['content' => null]])
                ->andWhere(['!=', 'content', ''])
                ->andWhere(['status' => CollegeContent::STATUS_ACTIVE])
                ->all();
            if (!empty($existingContent)) {
                $i = 1;
                foreach ($existingContent as $subPage) {
                    $isExits = CollegeHelper::$subPages[$subPage->sub_page] ?? '';
                    if ($isExits) {
                        $subPageArray[$i]['id'] = $subPage->sub_page;
                        $subPageArray[$i]['name'] = CollegeHelper::$subPages[$subPage->sub_page];
                    }
                    $i++;
                }
            }
            return ['output' =>  $subPageArray, 'selected' => ''];
        } else {
            return ['output' => '', 'selected' => ''];
        }
    }
}
