<?php
/**
 * Test script to verify the processAllListingTemplate optimization
 * This script compares the old method (with database queries) vs new method (using Elasticsearch data)
 */

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/common/config/main.php';

use common\services\CollegeService;
use frontend\models\CollegeSearch;
use yii\helpers\ArrayHelper;

// Initialize Yii application
$config = require __DIR__ . '/console/config/main.php';
new yii\console\Application($config);

echo "Testing processAllListingTemplate optimization...\n\n";

$collegeService = new CollegeService();

// Test template content with various placeholders
$testTemplate = '
Total Colleges: {Colleges_Count}
Public Colleges: {Public_College_Count}
Private Colleges: {Private_Colleges_Count}
Engineering Colleges: {Engineering_College_Count}
Management Colleges: {Management_College_Count}
Top NIRF Colleges: {Top_10_College_Names_as per NIRF Ranking}
Top Exams: {Entrance_Exam_Names}
Current Year: {Latest_Year}
';

echo "Template content:\n";
echo $testTemplate . "\n";
echo str_repeat('-', 50) . "\n";

try {
    // Test 1: Original method (with database queries)
    echo "1. Testing original method (with database queries)...\n";
    $startTime = microtime(true);
    $originalResult = $collegeService->processAllListingTemplate($testTemplate);
    $originalTime = microtime(true) - $startTime;
    
    echo "Original method result:\n";
    echo $originalResult . "\n";
    echo 'Time taken: ' . number_format($originalTime, 4) . " seconds\n\n";
    
    // Test 2: Optimized method (using mock Elasticsearch data)
    echo "2. Testing optimized method (using mock Elasticsearch data)...\n";
    
    // Create mock Elasticsearch data similar to what would come from the all colleges page
    $mockElasticData = [
        [
            'college_id' => 1,
            'name' => 'IIT Delhi',
            'display_name' => 'Indian Institute of Technology Delhi',
            'state_name' => 'Delhi',
            'type' => 'Public',
            'nirf_rank_overall' => 2,
            'exams' => ['JEE Advanced', 'JEE Main'],
            'course' => [
                [
                    'stream_name' => 'Engineering',
                    'course_name' => 'B.Tech'
                ]
            ],
            'minAvgFees' => 200000,
            'maxAvgFees' => 250000
        ],
        [
            'college_id' => 2,
            'name' => 'IIM Ahmedabad',
            'display_name' => 'Indian Institute of Management Ahmedabad',
            'state_name' => 'Gujarat',
            'type' => 'Public',
            'nirf_rank_overall' => 1,
            'exams' => ['CAT', 'GMAT'],
            'course' => [
                [
                    'stream_name' => 'Management',
                    'course_name' => 'MBA'
                ]
            ],
            'minAvgFees' => 2300000,
            'maxAvgFees' => 2500000
        ],
        [
            'college_id' => 3,
            'name' => 'AIIMS Delhi',
            'display_name' => 'All India Institute of Medical Sciences Delhi',
            'state_name' => 'Delhi',
            'type' => 'Public',
            'nirf_rank_overall' => 1,
            'exams' => ['NEET'],
            'course' => [
                [
                    'stream_name' => 'Medical',
                    'course_name' => 'MBBS'
                ]
            ],
            'minAvgFees' => 5000,
            'maxAvgFees' => 10000
        ]
    ];
    
    $searchModel = new CollegeSearch();
    $streamMap = [11, 14, 6, 16, 13]; // Engineering, Management, Commerce, Medical, Law
    
    $preComputedData = [
        'stats' => $collegeService->extractStatsFromElasticData($mockElasticData, $searchModel),
        'nirfColleges' => $collegeService->extractNirfCollegesFromElasticData($mockElasticData),
        'streamCounts' => $collegeService->extractStreamCountsFromElasticData($mockElasticData, $streamMap),
        'allStreamsTopColleges' => $collegeService->extractTopCollegesByStreamFromElasticData($mockElasticData, $streamMap, 5)
    ];
    
    $startTime = microtime(true);
    $optimizedResult = $collegeService->processAllListingTemplate($testTemplate, $preComputedData);
    $optimizedTime = microtime(true) - $startTime;
    
    echo "Optimized method result:\n";
    echo $optimizedResult . "\n";
    echo 'Time taken: ' . number_format($optimizedTime, 4) . " seconds\n\n";
    
    // Compare results
    echo str_repeat('=', 50) . "\n";
    echo "COMPARISON RESULTS:\n";
    echo str_repeat('=', 50) . "\n";
    
    $timeSaved = $originalTime - $optimizedTime;
    $percentImprovement = ($timeSaved / $originalTime) * 100;
    
    echo 'Original method time: ' . number_format($originalTime, 4) . " seconds\n";
    echo 'Optimized method time: ' . number_format($optimizedTime, 4) . " seconds\n";
    echo 'Time saved: ' . number_format($timeSaved, 4) . " seconds\n";
    echo 'Performance improvement: ' . number_format($percentImprovement, 2) . "%\n\n";
    
    // Check if results are similar (allowing for differences due to mock data)
    $originalLines = explode("\n", trim($originalResult));
    $optimizedLines = explode("\n", trim($optimizedResult));
    
    echo "Structure comparison:\n";
    echo 'Original result has ' . count($originalLines) . " lines\n";
    echo 'Optimized result has ' . count($optimizedLines) . " lines\n";
    
    if (count($originalLines) == count($optimizedLines)) {
        echo "✓ Both results have the same structure\n";
    } else {
        echo "⚠ Results have different structures\n";
    }
    
    echo "\nOptimization completed successfully!\n";
    echo "The optimized method uses data from the all colleges page instead of making separate database queries.\n";
} catch (Exception $e) {
    echo 'Error during testing: ' . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
