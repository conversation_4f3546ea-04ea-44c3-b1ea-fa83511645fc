<?php

use common\helpers\ContentHelper;
use common\helpers\DataHelper;
use frontend\assets\AppAsset;
use frontend\helpers\Ad;
use frontend\helpers\Freestartads;
use frontend\helpers\SeoExperimentHelper;
use frontend\helpers\Url;
use yii\helpers\Html;

//utils
$isMobile = Yii::$app->devicedetect->isMobile();
$this->title = !empty($seoInfo['meta_title']) ? $seoInfo['meta_title'] : $seoInfo['title'];
$this->context->description = !empty($seoInfo['meta_description']) ? $seoInfo['meta_description'] : '';
$this->context->ogImage = Url::toDefaultCollegeBanner();
$this->params['dynamicCta'] = empty($dynamicCta) ? [] : $dynamicCta;
$stream = $searchModel->stream ?? '';

// breadcrumb
if (!Yii::$app->request->isAjax) {
    $this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
    if (!empty($seoInfo['h1'])) {
        $this->params['breadcrumbs'][] = ['label' => 'Colleges', 'url' => '/all-colleges', 'title' => 'Colleges'];
        $this->params['breadcrumbs'][] = $seoInfo['h1'];
    } else {
        $this->params['breadcrumbs'][] = 'Colleges';
    }
}
$this->params['entity_name'] = 'College-Listing-Page';
$this->params['entity'] = 'college-listing';

if (!empty($sponsorParams)) {
    $this->params['sponsorParams'] = $sponsorParams;
}
// page specific assets
$this->params['canonicalUrl'] =  Url::base(true) . '/' . Yii::$app->request->getPathInfo();
// $this->registerCssFile('/yas/css/version2/college.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile('/yas/css/version2/college-landing-new.css', ['depends' => [AppAsset::class]]);
$this->registerJsFile(Yii::$app->params['jsPath'] . 'college-filter-page.js', ['depends' => [AppAsset::class]]);

$this->registerCss('.blueBgDiv.mobileOnly { display: none!important; }');

$year = \Yii::$app->params['Year'];
$interestedInPages = ['overview', 'important-dates', 'syllabus', 'previous-years-papers', 'exam-pattern'];
?>

<?php
if (Yii::$app->request->isAjax):
    ?>
    <div class="ajaxBreadCrumbDiv">
        <?= $this->render('partials/_college-search-breadcrumbs', [
            'seoInfo' => $seoInfo
        ]) ?>
    </div>
<?php endif; ?>
<div class="college__Landing__New">
    <div class="college__Landing__Hero__Section1">
        <?php
      
        echo $this->render('partials/_college-filter-seo', [
            'seoInfo' => $seoInfo,
            'year' => $year,
            'collegeListingNotification' => $collegeListingNotification ?? [],
        ]);
        ?>
    </div>
    <?php if (!$isMobile): ?>
        <aside>
            <div class="horizontalRectangle">
                <div class="appendAdDiv xs-h100" style="background:#EAEAEA;">
                    <?php /* if ($isMobile) : ?>
                                    <?php echo Ad::unit('GMU_NEW_LISTING_PAGE_WAP_300x100_ATF', '[300,100]') ?>
                                <?php else : */ ?>
                    <?php //echo Ad::unit('GMU_NEW_LISTING_PAGE_WEB_1206x90_ATF', '[1206,90]')
                    ?>
                    <?php echo Freestartads::unit('getmyuni-com_leaderboard_atf', '__728x90')
                    ?>
                    <?php //endif;
                    ?>
                </div>
            </div>
        </aside>
    <?php endif; ?>
    <div class="row">
        <div class="all-college-ajax desktop__filter col-md-3 lg-pr-0" style="min-height: 100vh;">
            <?php if ($isMobile): ?>
                <?= $this->render('partials/_college-filter-search-mobile-new-design', [
                    'filters' => $filters,
                    'model' => $searchModel,
                    'selectedFilters' => $selectedFilters,
                    'totalCollegeCount' => $totalCount,
                    'sort' => $sort,
                    'isContent' => empty($seoInfo['content']) ? false : true,

                ]) ?>
            <?php endif ?>

            <?php if (!$isMobile): ?>
                <?= $this->render('partials/_college-filter-search-new-design', [
                    'filters' => $filters,
                    'model' => $searchModel,
                    'selectedFilters' => $selectedFilters,
                    'totalCollegeCount' => $totalCount,
                ]) ?>
            <?php endif ?>



        </div>
        <div class="col-md-6 mobile__white__bg">
            <div class="sort__row__container mobile__version">
                <button class="filter__button mobileFilter">
                    <i class="spriteIcon__2 filter__icon"></i>
                    Filter
                    <span class="number__of__filters">
                        <?php echo count($selectedFilters) > 0  ? count($selectedFilters) : 0; ?>
                    </span>
                </button>
                <div class="sortBy__select2__container">
                    <span>Sort By:</span>
                    <?= Html::dropDownList('college-sort', 'rank', [
                        'position' => 'Popularity',
                        'rank' => 'Ranking',
                        'highest_fee' => 'Highest Fees',
                        'lowest_fee' => 'Lowest Fees',
                    ], ['id' => 'college-sort']) ?>
                </div>
            </div>
            <div class="filter__selected__container">
                <?php if (!empty($selectedFilters)): ?>
                    <div id="selectedFilters" class="filterDiv">
                        <?php foreach ($selectedFilters as $filter): ?>
                            <button id="<?= $filter['slug'] ?>" data-attr="<?= $filter['mapped_field'] ?>" class="filter__selected">
                                <?= $filter['name'] ?><i class="spriteIcon small__close__icon remove-college-filter"></i></button>
                        <?php endforeach; ?>
                    </div>
                    <div class="mobile__clear__filter">
                        <?php if (!empty($selectedFilters)):
                            ?><span id="clearAllClg" class="clearAll">Clear All</span><?php
                        endif; ?>
                    </div>
                <?php endif; ?>
            </div>

            <div class="sort__row__container">
                <h3 class="filtered__college_count">Showing <?= $totalCount ?? '' ?> Colleges</h3>
                <div class="sortBy__select2__container desktopOnly">
                    <span>Sort By:</span>
                    <?= Html::dropDownList('college-sort', 'rank', [
                        'position' => 'Popularity',
                        'rank' => 'Ranking',
                        'highest_fee' => 'Highest Fees',
                        'lowest_fee' => 'Lowest Fees',
                    ], ['id' => 'college-sort']) ?>
                </div>
            </div>
            <div class="searchBar">
                <input class="search-autocomplete" id="autoComplete" autocomplete="off"
                    placeholder="Search for Colleges" type="text" tabindex="1">
                <div class="selection"></div>
            </div>
            <div class="filtered__colleges__list">
                <?php
                    
              
                ?>

            </div>

        </div>
        <div class="col-md-3">

        </div>
    </div>
    <?php
   
    ?>
    <section class="faq_section">
        <?php if (!empty($faqs)): ?>
            <h2>FAQs</h2>
            <?= $this->render('partials/_faq-card', [
                'faqs' => $faqs,
            ]) ?>

        <?php endif; ?>
    </section>
    <section class="also__explore">
        <?php
        echo $this->render('partials/_college-filter-explore-colleges', [
            'models' => $colleges,
            // 'elasticmodels' => $elasticData,
            // 'liveAppModels' => $liveAppColleges,
            'iRank' => $iRank ?? 1,
            'hasNext' => $hasNext,
            'page' => $page,
            'isMobile' => $isMobile,
            'searchModel' => $searchModel
        ]);
        ?>

    </section>
    <div class="interestedExam">
        <?php if (!empty($intrestedExams)) { ?>
            <?php
              echo $this->render('partials/_cards', [
                'examSlug' => '',
                'title' => 'Other ' . $stream . ' Exams',
                'totalCards' => 8,
                'pageSlugCount' => 5,
                'pages' => $interestedInPages,
                'details' => $intrestedExams,
                'assetUrl' => Yii::getAlias('@getmyuniExamAsset/'),
              ])

            ?>
        <?php } ?>
    </div>

    <section class="news__update__section test">
        <?php
          echo $this->render('partials/_college-filter-news-update', [
            'models' => $colleges,
            'latestArticles' => $latestArticles,
            'latestNews' => $latestNews,
            'iRank' => $iRank ?? 1,
            'hasNext' => $hasNext,
            'page' => $page,
            'isMobile' => $isMobile,
            'searchModel' => $searchModel
          ]);
            ?>

    </section>
</div>
</div>


<?php

$filCou = $searchModel->course ?? '';
$filStr = $searchModel->stream ?? '';
$ctaName = 'colleges_listing_card_predict_my_college';
$preCta = 'colleges_listing_scholarship_wap_top_sticky_cta';

?>
<?php if ($isMobile): ?>
    <div class="mobileOnly primaryBtn brochureBtn filter-college-scholership leadFilterData predict-my-college-cta" data-filter="college-listing" data-entity="college" data-lead_cta="22" data-courseslug="<?= $model->course ?? ''; ?>" data-stream="<?= $model->stream ?? ''; ?>" data-ctalocation="<?= empty($dynamicCtaFilter) && empty($dynamicCtaFilter['cta_position_3']) || empty(array_filter($dynamicCtaFilter['cta_position_3'])) ? $preCta : ($dynamicCtaFilter['cta_position_3']['wap'] ?? $preCta) ?>"></div>

<?php else: ?>
    <div class="desktopOnly getSupport college-filter-get-support">
        <!-- <p class="getSupport__subheading">Your Dream College Awaits!</p> -->
        <div class="brochureBtn filter-college-scholership leadFilterData predict-my-college-cta" data-filter="college-listing" data-entity="college" data-lead_cta="22" data-courseslug="<?= $filCou ?? ''; ?>" data-stream="<?= $filStr ?? ''; ?>" data-ctalocation="<?= empty($dynamicCtaFilter) && empty($dynamicCtaFilter['cta_position_3']) || empty(array_filter($dynamicCtaFilter['cta_position_3'])) ? $ctaName : ($dynamicCtaFilter['cta_position_3']['web'] ?? $ctaName) ?>"></div>
    </div>
<?php endif; ?>
</div>