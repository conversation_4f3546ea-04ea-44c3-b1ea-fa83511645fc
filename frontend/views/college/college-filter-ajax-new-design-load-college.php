<?php
$isMobile = Yii::$app->devicedetect->isMobile();
?>

<?php
if (Yii::$app->request->isAjax):
    ?>
   
            <div class="filtered__colleges__list">
                <?php
                echo $this->render('partials/_college-filter-list-new-design', [
                    'models' => $colleges,
                    // 'elasticmodels' => $elasticData,
                    // 'liveAppModels' => $liveAppColleges,
                    'iRank' => $iRank ?? 1,
                    'hasNext' => $hasNext,
                    'page' => $page,
                    'isMobile' => $isMobile,
                    'searchModel' => $searchModel,
                    'isWidget' => $isWidget,
                    'filters' => $filters,
                    'selectedFilters' => $selectedFilters,
                    'sponsorCollegeList' => $sponsorCollegeList,
                    'totalCollegeCount' => $totalCount ?? 0,
                    'urlFilter' => $url,
                ]);
                ?>
            </div>
           
<?php endif  ?>       