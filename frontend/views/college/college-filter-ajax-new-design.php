<?php

use frontend\assets\AppAsset;
use frontend\helpers\Url;
use yii\helpers\Html;
use frontend\helpers\Ad;
use common\services\ExamService;
use frontend\helpers\Freestartads;

//utils
$examService = new ExamService();
$isMobile = Yii::$app->devicedetect->isMobile();
$this->title = !empty($seoInfo['meta_title']) ? $seoInfo['meta_title'] : $seoInfo['title'];
$this->context->description = !empty($seoInfo['meta_description']) ? $seoInfo['meta_description'] : '';
$this->context->ogImage = Url::toDefaultCollegeBanner();
$this->params['dynamicCta'] = empty($dynamicCta) ? [] : $dynamicCta;
$interestedInPages = ['overview', 'important-dates', 'syllabus', 'previous-years-papers', 'exam-pattern'];
$stream = $searchModel->stream ?? '';

// breadcrumb
if (!Yii::$app->request->isAjax) {
    $this->params['breadcrumbs'][] = ['label' => 'Home', 'url' => ['/'], 'title' => 'Home'];
    if (!empty($seoInfo['h1'])) {
        $this->params['breadcrumbs'][] = ['label' => 'Colleges', 'url' => '/all-colleges', 'title' => 'Colleges'];
        $this->params['breadcrumbs'][] = $seoInfo['h1'];
    } else {
        $this->params['breadcrumbs'][] = 'Colleges';
    }
}
$this->params['entity_name'] = 'College-Listing-Page';
$this->params['entity'] = 'college-listing';

if (!empty($sponsorParams)) {
    $this->params['sponsorParams'] = $sponsorParams;
}
// page specific assets
$this->params['canonicalUrl'] =  Url::base(true) . '/' . Yii::$app->request->getPathInfo();
// $this->registerCssFile('/yas/css/version2/college.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile('/yas/css/version2/college-landing-new.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile('/yas/css/version2/college-compare-panel.css', ['depends' => [AppAsset::class]]);
$this->registerJsFile(Yii::$app->params['jsPath'] . 'college-filter-page.js', ['depends' => [AppAsset::class]]);

$this->registerCss('.blueBgDiv.mobileOnly { display: none!important; }');

$year = \Yii::$app->params['Year'];
?>

<?php
if (Yii::$app->request->isAjax):
    ?>
    <div class="ajaxBreadCrumbDiv">
        <?= $this->render('partials/_college-search-breadcrumbs', [
            'seoInfo' => $seoInfo
        ]) ?>
    </div>
<?php endif; ?>

<div class="pageRedirectionLinks collegeCategoryType">
    <p class="btn_left over">
        <i class="spriteIcon left_angle"></i>
    </p>
    <p class="btn_right">
        <i class="spriteIcon right_angle"></i>
    </p>
    <ul class="tabList">
        <li data-tab="" class="activeLink">Overview</li>
        <?php if (!empty($filterMapping)): ?>
            <?php foreach ($filterMapping as $value): ?>
                <a href="<?= Url::toDomain() . $value['groupingPage'] ?>" title="<?= $value['groupingPage'] ?>">
                    <li data-tab=""><?= $value['h1'] ?></li>
                </a>
            <?php endforeach; ?>
        <?php endif; ?>
    </ul>
</div>

<div class="college__Landing__New">
    <?php /*
    <div class="container top__ads">
        <div class="row">
            <div class="col-md-4">
                <?php echo Freestartads::unit('getmyuni-com_leaderboard_atf', '__320x100') ?>
            </div>
            <div class="col-md-4 desktopOnly">
                <?php echo Freestartads::unit('getmyuni-com_siderail_right', '__300x600') ?>
            </div>
            <div class="col-md-4 desktopOnly">
                <?php echo Freestartads::unit('getmyuni-com_siderail_right_2', '__300x600') ?>
            </div>
        </div>
    </div>
    */ ?>
    <div class="college__Landing__Hero__Section1">
        <?php if (!empty($allListingTemplate)): ?>
            <div class="college__Landing__Hero__Section pageData pageInfo" style="margin: 20px 0; padding: 15px; background: #f9f9f9; border-radius: 5px;">
                <?= $allListingTemplate ?>
            </div>
        <?php else: ?>
            <?php
            echo $this->render('partials/_college-filter-seo', [
                'seoInfo' => $seoInfo,
                'year' => !empty($seoInfo->localize_year) ?  $seoInfo->localize_year : $year,
                'collegeListingNotification' => $collegeListingNotification ?? [],
            ]);
            ?>
        <?php endif; ?>
    </div>

    <?php if (!$isMobile): ?>
        <!--aside>
                                                        <div class="horizontalRectangle">
                                                                <div class="appendAdDiv xs-h100" style="background:#EAEAEA;">
                                                                <?php /* if ($isMobile) : ?>
                                                                        <?php echo Ad::unit('GMU_NEW_LISTING_PAGE_WAP_300x100_ATF', '[300,100]') ?>
                                                                <?php else : */ ?>
                                                                <?php //echo Ad::unit('GMU_NEW_LISTING_PAGE_WEB_1206x90_ATF', '[1206,90]')
                                                                ?>
                                                                <?php //endif;
                                                                ?>
                                                                </div>
                                                        </div>
                                                        </aside-->
    <?php endif; ?>
</div>
<div class="row">
    <div class="all-college-ajax  col-md-3 lg-pr-0" style="min-height: 100vh;">

    </div>
    <?php if ($isMobile) { ?>
        <div class="sort__row__container mobile__version filter__header__sticky">
            <button class="filter__button mobileFilter">
                <i class="spriteIcon__2 filter__icon"></i>
                Filter
                <span class="number__of__filters">
                    <?php echo count($selectedFilters) > 0  ? count($selectedFilters) : 0; ?>
                </span>
            </button>
            <div class="sortBy__select2__container">
                <span>Sort By:</span>
                <?= Html::dropDownList('college-sort', $isStream, [
                    'position' => 'Popularity',
                    'rank' => 'Ranking',
                    'highest_fee' => 'Highest Fees',
                    'lowest_fee' => 'Lowest Fees',
                ], ['id' => 'college-sort']) ?>
            </div>
        </div>
    <?php } ?>
    <div class="col-md-6 mobile__white__bg">
        <div class="filter__selected__container">
            <?php if (!empty($selectedFilters)): ?>
                <div id="selectedFilters" class="filterDiv">
                    <?php foreach ($selectedFilters as $filter): ?>
                        <button id="<?= $filter['slug'] ?>" data-attr="<?= $filter['mapped_field'] ?>" class="filter__selected">
                            <?= $filter['name'] ?><i class="spriteIcon small__close__icon remove-college-filter"></i></button>

                    <?php endforeach; ?>
                </div>
                <div class="mobile__clear__filter">
                    <?php if (!empty($selectedFilters)):
                        ?><span id="clearAllClg" class="clearAll">Clear All</span><?php
                    endif; ?>
                </div>
            <?php endif; ?>
        </div>

        <div class="sort__row__container">
            <h3 class="filtered__college_count">Showing <?= $totalCount ?? '' ?><?php if ($totalCount == 1) {
                ?> College <?php
                                                        } else {
                                                            ?> Colleges <?php
                                                        } ?></h3>
            <?php if (!$isMobile) { ?>
                <div class="sortBy__select2__container desktopOnly">
                    <span>Sort By:</span>
                    <?= Html::dropDownList('college-sort', $isStream, [
                        'position' => 'Popularity',
                        'rank' => 'Ranking',
                        'highest_fee' => 'Highest Fees',
                        'lowest_fee' => 'Lowest Fees',
                    ], ['id' => 'college-sort']) ?>
                </div>
            <?php } ?>
        </div>
        <div class="searchBar">
            <input class="search-autocomplete1 search-listing" id="autoComplete" autocomplete="off" placeholder="Search for Colleges" type="text" tabindex="1">
            <i class="spriteIcon small__close__icon search-remove"></i>
            <div class="selection"></div>
        </div>
        <div class="filtered__colleges__list">
            <?php
            echo $this->render('partials/_college-filter-list-new-design', [
                'models' => $colleges,
                // 'elasticmodels' => $elasticData,
                // 'liveAppModels' => $liveAppColleges,
                'iRank' => $iRank ?? 1,
                'hasNext' => $hasNext,
                'page' => $page,
                'isMobile' => $isMobile,
                'searchModel' => $searchModel,
                'isWidget' => $isWidget,
                'filters' => $filters,
                'selectedFilters' => $selectedFilters,
                'sponsorCollegeList' => $sponsorCollegeList ?? [],
                'totalCollegeCount' => $totalCount ?? 0,
                'urlFilter' => '',
            ]);
            ?>

        </div>

    </div>
    <div class="col-md-3 desktopOnly">
        <?php echo Freestartads::unit('getmyuni-com_siderail_right_2', '__300x600') ?>
        <?php echo Freestartads::unit('getmyuni-com_siderail_right', '__300x600')
        ?>
    </div>
    <div class="col-md-3 mobileOnly">
        <?php echo Freestartads::unit('getmyuni-com_siderail_right', '__300x250')
        ?>
    </div>
</div>
<div class="interestedExam">
    <?php if (!empty($intrestedExams)) {  ?>
        <?=
        $this->render('partials/_cards', [
            'examSlug' => '',
            'title' => 'Other ' . $stream . ' Exams',
            'totalCards' => 8,
            'pageSlugCount' => 5,
            'pages' => $interestedInPages,
            'details' => $intrestedExams,
            'assetUrl' => Yii::getAlias('@getmyuniExamAsset/'),
        ])
        ?>
    <?php } ?>
</div>

<?php
/*echo $this->render('partials/_college-filter-other-colleges', [
                                                        'models' => $colleges,
                                                        // 'elasticmodels' => $elasticData,
                                                        // 'liveAppModels' => $liveAppColleges,
                                                        'iRank' => $iRank ?? 1,
                                                        'hasNext' => $hasNext,
                                                        'page' => $page,
                                                        'isMobile' => $isMobile,
                                                        'searchModel' => $searchModel
                                                ]);*/
?>
<div class="filter-faq">
    <?php if (!empty($faqs)): ?>
        <?= $this->render('partials/_faq-card-filter', [
            'faqs' => $faqs,
        ]) ?>

    <?php endif; ?>
</div>

<!--section class="also__explore">
                        <?php
                        /*   echo $this->render('partials/_college-filter-explore-colleges', [
                                                        'models' => $colleges,
                                                        // 'elasticmodels' => $elasticData,
                                                        // 'liveAppModels' => $liveAppColleges,
                                                        'iRank' => $iRank ?? 1,
                                                        'hasNext' => $hasNext,
                                                        'page' => $page,
                                                        'isMobile' => $isMobile,
                                                        'searchModel' => $searchModel,
                                                        'filters' => $filters,
                                                        'selectedFilters'=>$selectedFilters

                                                ]); */
                        ?>
                              
                        </section-->
<section class="news__update__section testing">
    <?php
    echo $this->render('partials/_college-filter-news-update', [
        'models' => $colleges,
        'latestArticles' => $latestArticles,
        'latestNews' => $latestNews,
        'iRank' => $iRank ?? 1,
        'hasNext' => $hasNext,
        'page' => $page,
        'isMobile' => $isMobile,
        'searchModel' => $searchModel
    ]);
    ?>
</section>
</div>
</div>



<?php
$filCou = $searchModel->course ?? '';
$filStr = $searchModel->stream ?? '';
$ctaName = 'colleges_listing_card_predict_my_college';
$preCta = 'colleges_listing_scholarship_wap_top_sticky_cta';

?>
<?php if ($isMobile): ?>
    <div class="mobile-bottom-cta  primaryBtn brochureBtn filter-college-scholership leadFilterData predict-my-college-cta" data-filter="college-listing" data-entity="college" data-lead_cta="22" data-courseslug="<?= $model->course ?? ''; ?>" data-stream="<?= $model->stream ?? ''; ?>" data-ctalocation="<?= empty($dynamicCtaFilter) && empty($dynamicCtaFilter['cta_position_3']) || empty(array_filter($dynamicCtaFilter['cta_position_3'])) ? $preCta : ($dynamicCtaFilter['cta_position_3']['wap'] ?? $preCta) ?>"></div>

<?php else: ?>
    <div class="desktopOnly getSupport college-filter-get-support">
        <!-- <p class="getSupport__subheading">Your Dream College Awaits!</p> -->
        <div class="brochureBtn filter-college-scholership leadFilterData predict-my-college-cta" data-filter="college-listing" data-entity="college" data-lead_cta="22" data-courseslug="<?= $filCou ?? ''; ?>" data-stream="<?= $filStr ?? ''; ?>" data-ctalocation="<?= empty($dynamicCtaFilter) && empty($dynamicCtaFilter['cta_position_3']) || empty(array_filter($dynamicCtaFilter['cta_position_3'])) ? $ctaName : ($dynamicCtaFilter['cta_position_3']['web'] ?? $ctaName) ?>"></div>
    </div>
<?php endif; ?>
</div>
<div id="college_compare_header_select_panel"></div>
<div class="feedback__container">

    <form class="feedback__form" action="#" id="feedback-form">
        <div class="errorHtml">

        </div>
        <span class="spriteIcon closeIcon"></span>
        <div class="show_form">
            <p class="text1">Your opinion matters to us!</p>
            <p class="text2">We will use this feedback to improve your experience.</p>
            <ul class="rating__buttons">
                <li data-value="1">
                    <input type="radio" name="rating" id="rating1" value="1">
                    <label for="rating1">1</label>
                </li>
                <li data-value="2">
                    <input type="radio" name="rating" id="rating2" value="2">
                    <label for="rating2">2</label>
                </li>
                <li data-value="3">
                    <input type="radio" name="rating" id="rating3" value="3">
                    <label for="rating3">3</label>
                </li>
                <li data-value="4">
                    <input type="radio" name="rating" id="rating4" value="4">
                    <label for="rating4">4</label>
                </li>
                <li data-value="5">
                    <input type="radio" name="rating" id="rating5" value="5">
                    <label for="rating5">5</label>
                </li>
                <li data-value="6">
                    <input type="radio" name="rating" id="rating6" value="6">
                    <label for="rating6">6</label>
                </li>
                <li data-value="7">
                    <input type="radio" name="rating" id="rating7" value="7">
                    <label for="rating7">7</label>
                </li>
                <li data-value="8">
                    <input type="radio" name="rating" id="rating8" value="8">
                    <label for="rating8">8</label>
                </li>
                <li data-value="9">
                    <input type="radio" name="rating" id="rating9" value="9">
                    <label for="rating9">9</label>
                </li>
                <li data-value="10">
                    <input type="radio" name="rating" id="rating10" value="10">
                    <label for="rating10">10</label>
                </li>
            </ul>
            <p class="text3">You Rated Below Average</p>
            <input type="hidden" name="rating_option_text" class="rating_option_text" value="">
            <input type="hidden" name="url" class="current-url" value="">
            <p class="text4">I found the page information</p>
            <ul class="feedback__buttons">
                <li class="one-to-four" data-value="1">
                    <input type="radio" name="rating_option" id="feedback1" value="Incorrect">
                    <label for="feedback1">Incorrect</label>
                </li>
                <li class="one-to-four" data-value="2">
                    <input type="radio" name="rating_option" id="feedback2" value="Irrelevant">
                    <label for="feedback2">Irrelevant</label>
                </li>
                <li class="one-to-four" data-value="3">
                    <input type="radio" name="rating_option" id="feedback3" value="Insufficient">
                    <label for="feedback3">Insufficient</label>
                </li>
                <li class="one-to-four" data-value="4">
                    <input type="radio" name="rating_option" id="feedback4" value="Confusing">
                    <label for="feedback4">Confusing</label>
                </li>

                <li class="five-to-seven" data-value="5">
                    <input type="radio" name="rating_option" id="feedback5" value="Not-up-to-date">
                    <label for="feedback5">Not up-to-date</label>
                </li>
                <li class="five-to-seven" data-value="6">
                    <input type="radio" name="rating_option" id="feedback6" value="Insufficient">
                    <label for="feedback6">Insufficient</label>
                </li>
                <li class="five-to-seven" data-value="7">
                    <input type="radio" name="rating_option" id="feedback7" value="Helpful">
                    <label for="feedback7">Helpful</label>
                </li>

                <li class="eight-to-ten" data-value="8">
                    <input type="radio" name="rating_option" id="feedback8" value="Found-Relevant">
                    <label for="feedback8">Found Relevant</label>
                </li>
                <li class="eight-to-ten" data-value="9">
                    <input type="radio" name="rating_option" id="feedback9" value="Easy-to-read">
                    <label for="feedback9">Easy to read</label>
                </li>
                <li class="eight-to-ten" data-value="10">
                    <input type="radio" name="rating_option" id="feedback10" value="Up-to-date">
                    <label for="feedback10">Up to date</label>
                </li>
                <li class="eight-to-ten" data-value="11">
                    <input type="radio" name="rating_option" id="feedback11" value="Precise">
                    <label for="feedback11">Precise</label>
                </li>
            </ul>
            <div id="option-error-one" class="error__position"></div>
            <p class="text4">Please provide your feedback so that we can improve your experience.</p>
            <textarea class="write__feedback" placeholder="Write here..." name="experience_text"></textarea>
            <div id="option-error-two" class="error__position"></div>
            <button class="primaryBtn feed-back-button">Submit</button>
        </div>
        <div class="hide_form" style="display:none">
            <img src="../../yas/images/lead-form-thankyou.png" loading="lazy">
            <img src="../../yas/images/lead-form-final-logo.png" class="lead__form__logo" loading="lazy">
            <p class="feedback-text">Thanks for FeedBack </p>
        </div>
    </form>


</div>