<?php

namespace common\models;

use common\event\CollegeFilterEvent;
use common\event\SitemapEvent;
use common\services\CollegeService;
use Exception;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

use Yii;

/**
 * This is the model class for table "college_program".
 *
 * @property int $id
 * @property int|null $program_id
 * @property int|null $college_id
 * @property string|null $mode
 * @property string|null $type
 * @property string|null $application_link
 * @property string|null $salary
 * @property string|null $duration
 * @property string|null $duration_type
 * @property string|null $seat
 * @property int|null $position
 * @property int|null $page_index
 * @property int|null $status
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property College $college
 * @property Program $program
 */
class CollegeProgram extends \yii\db\ActiveRecord
{

    public $specialization_id;
    public $exam_id;
    public $company_id;

    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;

    const IS_HONS_YES = 1;
    const IS_HONS_NO = 0;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'college_program';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'updatedAtAttribute' => false,
                'value' => new \yii\db\Expression('NOW()'),
            ],
            'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }


    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['program_id', 'college_id', 'course_id', 'position', 'page_index', 'status', 'created_by', 'updated_by', 'duration_type'], 'integer'],
            [['created_at', 'updated_at', 'specialization_id', 'exam_id', 'company_id', 'mode', 'type', 'duration', 'duration_type', 'seat'], 'safe'],
            [['application_link', 'salary'], 'string', 'max' => 255],
            [['program_id', 'college_id'], 'required'],
            [['college_id', 'course_id', 'program_id'], 'unique', 'on' => 'create', 'targetAttribute' => ['college_id', 'course_id', 'program_id']],
            [['college_id'], 'exist', 'skipOnError' => true, 'targetClass' => College::className(), 'targetAttribute' => ['college_id' => 'id']],
            [['course_id'], 'exist', 'skipOnError' => true, 'targetClass' => Course::className(), 'targetAttribute' => ['course_id' => 'id']],
            [['program_id'], 'exist', 'skipOnError' => true, 'targetClass' => Program::className(), 'targetAttribute' => ['program_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'program_id' => 'Program ID',
            'college_id' => 'College ID',
            'course_id' => 'Course ID',
            'application_link' => 'Application Link',
            'salary' => 'Salary',
            'duration' => 'Duration',
            'duration_type' => 'Duration Type',
            'seat' => 'Seat',
            'position' => 'Position',
            'page_index' => 'Page Index',
            'status' => 'Status',
            'created_by' => 'Created By',
            'updated_by' => 'Updated By',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[College]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CollegeQuery
     */
    public function getCollege()
    {
        return $this->hasOne(College::className(), ['id' => 'college_id']);
    }

    /**
     * Gets query for [[Program]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\ProgramQuery
     */
    public function getProgram()
    {
        return $this->hasOne(Program::className(), ['id' => 'program_id']);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\CollegeProgramQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\CollegeProgramQuery(get_called_class());
    }

    /**
     * Gets query for [[CollegeProgram-Fees]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CollegeFeesQuery
     */
    public function getCollegeProgramFees()
    {
        return $this->hasMany(CollegeProgramFees::className(), ['college_program_id' => 'id']);
    }

    /**
     * Gets query for [[CollegeProgramContent]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CollegeProgramContentQuery
     */
    public function getCollegeProgramContent()
    {
        return $this->hasOne(CollegeProgramContent::className(), ['college_course_id' => 'id']);
    }

    /**
     * Gets query for [[Exams]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\ExamQuery
     */
    public function getExams()
    {
        return $this->hasMany(Exam::className(), ['id' => 'exam_id'])->viaTable('college_program_exam', ['college_program_id' => 'id']);
    }

    /**
     * Gets query for [[Exams]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CompnayQuery
     */
    public function getCompanys()
    {
        return $this->hasMany(Company::className(), ['id' => 'company_id'])->viaTable('college_course_company', ['course_id' => 'course_id']);
    }

    /**
     * Save the List of Exams applicable for College Course
     *
     *  @return void
     */
    public function saveExams(array $examIds = [])
    {

        if (empty($examIds) || empty($this->id)) {
            throw new Exception(CollegeProgram::class . 'id or Exam Ids are required.');
        }

        if (!$this->isNewRecord) {
            $this->unlinkAll('exams', true);
        }

        foreach ($examIds as $examId) {
            $examModel = Exam::findOne($examId);
            if (!$examModel) {
                continue;
            }
            $this->link('exams', $examModel);
        }
        CollegeService::updateCourseDocumentExam($examIds, $this->id);
        (new CollegeFilterEvent())->updateCollegeFilterExam($this->college_id);
        return true;
    }

    /**
     * Save the List of Companies applicable for College Course
     *
     *  @return void
     */
    public function saveCompnay($collegeId, $courseId, array $companyIds = [])
    {
        if (!$this->isNewRecord) {
            $this->unlinkAll('companys', true);
        }

        foreach ($companyIds as $companyId) {
            $companyModel = Company::find()->where(['id' => (int)$companyId])->one();

            if (!$companyModel) {
                continue;
            }

            $company = new CollegeCourseCompany();
            $company->course_id = $courseId;
            $company->college_id = $collegeId;
            $company->company_id = $companyId;
            $company->save();
        }

        return true;
    }

    public function afterSave($insert, $changedAttributes)
    {
        
        // update college course collections
        CollegeService::updateProgramDocument($this);
        CollegeService::updateCourseAvgFee($this);
        self::updateCollegeCourseMapping($this);


        //update filter
        (new CollegeFilterEvent())->updateCollegeFilter($this->college);
        (new SitemapEvent())->updateCollegeProgramSitemap($this, $this->college->slug, $this->college->status);
        // (new CollegeFilterEvent())->updateFilterMapping($this);
        // (new CollegeFilterEvent())->updateCourseFilter($this);
        if ($insert) {
            // New record inserted
            $collegeContent = CollegeContent::find()->select(['updated_at','id','content_updated_at'])
                          ->where(['entity_id'=>$this->college->id])
                          ->andWhere(['in', 'sub_page', ['info', 'courses-fees']])
                          ->all();
            foreach ($collegeContent as $content) {
                $content->scenario = CollegeContent::SCENARIO_IMPORTER;
                $content->content_updated_at = $content->updated_at;
                $content->updated_at = date('Y-m-d h:i:s');
                $content->save();
            }
        } if ($changedAttributes) {
            // Existing record updated
            $collegeContent = CollegeContent::find()->select(['updated_at','id','content_updated_at'])
            ->where(['entity_id'=>$this->college->id])
            ->andWhere(['in', 'sub_page', ['info', 'courses-fees']])
            ->all();
            foreach ($collegeContent as $content) {
                $content->scenario = CollegeContent::SCENARIO_IMPORTER;
                $content->content_updated_at = $content->updated_at;
                $content->updated_at = date('Y-m-d h:i:s');
                $content->save();
            }
        }
      
       

        return parent::afterSave($insert, $changedAttributes);
    }

    /**
     * Gets query for [[Course]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CourseQuery
     */
    public function getCourse()
    {
        return $this->hasOne(Course::className(), ['id' => 'course_id']);
    }


    /** Update the college respected course to the table */
    public function updateCollegeCourseMapping(CollegeProgram $collegeProgram)
    {
        $count = $this->getProgramList($collegeProgram->college_id, $collegeProgram->course_id);

        $model = CollegeCourseMapping::find()
            ->where(['college_id' => $collegeProgram->college_id])
            ->andWhere(['course_id' => $collegeProgram->course_id])
            ->one();

        if (!empty($count) && empty($model)) {
            if (!$model) {
                $model = new CollegeCourseMapping();
            }

            $model->college_id = $collegeProgram->college_id;
            $model->course_id = $collegeProgram->course_id;

            if ($model->save()) {
                return true;
            }
        } else if (empty($count) && !empty($model)) {
            $model->delete();
        }
        return false;
    }

    public function getProgramList($collegeId, $courseId)
    {
        return CollegeProgram::find()
            ->where(['college_id' => $collegeId])
            ->andWhere(['course_id' => $courseId])
            ->andWhere(['not', ['program_id' => null]])
            ->andWhere(['status' => CollegeProgram::STATUS_ACTIVE])
            ->one();
    }
}
