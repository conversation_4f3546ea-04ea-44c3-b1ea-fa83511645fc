<?php

namespace common\models;

use yii\db\ActiveRecord;
use yii\behaviors\TimestampBehavior;
use yii\helpers\ArrayHelper;


use Yii;

/**
 * This is the model class for table "college_rankings".
 *
 * @property int $id
 * @property int|null $college_id
 * @property int|null $publisher_id
 * @property string|null $criteria
 * @property int|null $rank
 * @property string|null $year
 * @property string|null $created_at
 * @property string|null $updated_at
 */
class CollegeRankings extends \yii\db\ActiveRecord
{
    const STATUS_INACTIVE = 0;
    const STATUS_ACTIVE = 1;
    const CRITERIA_COURSE = 'course';
    const CRITERIA_STREAM = 'stream';
    const CRITERIA_SPECIALIZATION = 'specialization';
    const CRITERIA_OTHER = 'other';
    const SCENARIO_IMPORTER = 'importer';
    const PUBLISHER_PI = 4;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'college_rankings';
    }

    public function behaviors()
    {
        return [

            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            //'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['publisher_id', 'college_id',  'rank', 'status', 'year'], 'required', 'except' => self::SCENARIO_IMPORTER],
            // [['college_id'], 'unique', 'targetAttribute' => 'publisher_id'],
            [['created_at', 'updated_at'], 'safe'],
            [['criteria'], 'string', 'max' => 255],

        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'college_id' => 'College ID',
            'publisher_id' => 'Publisher ID',
            'criteria' => 'Criteria',
            'rank' => 'Rank',
            'year' => 'Year',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'status' => 'Status'
        ];
    }

    /*
     Get Rank College Join
    */
    public function getCollege()
    {
        return $this->hasOne(College::className(), ['id' => 'college_id']);
    }

    /*
     Get Rank Publisher Join
    */
    public function getpublisher()
    {
        return $this->hasOne(CollegeRankingPublisher::className(), ['id' => 'publisher_id']);
    }

    /*
     Get Rank Course Join
    */
    public function getCourse()
    {
        return $this->hasOne(Course::className(), ['id' => 'criteria_id']);
    }

    /*
     Get Rank Stream Join
    */
    public function getStream()
    {
        return $this->hasOne(Stream::className(), ['id' => 'criteria_id']);
    }
    /*
     Get Rank Other Criteria Join
    */
    public function getOther()
    {
        return $this->hasOne(OtherRankings::className(), ['id' => 'criteria_id']);
    }

    /*
     Get Rank Other Specialization Join
    */

    public function getSpecialization()
    {
        return $this->hasOne(Specialization::className(), ['id' => 'criteria_id']);
    }

    public function afterSave($insert, $changedAttributes)
    {
          $collegeID = Yii::$app->request->get('college_id');
          
        if (!empty($insert) && !empty($collegeID)) {
            // New record inserted
            $collegeContent = CollegeContent::find()->select(['updated_at','id','content_updated_at'])
                       ->where(['entity_id'=>$collegeID])
                       ->andWhere(['in', 'sub_page', ['info', 'courses-fees']])
                       ->all();
            foreach ($collegeContent as $content) {
                  $content->scenario = CollegeContent::SCENARIO_IMPORTER;
                if (empty($content->content_updated_at)) {
                    $content->content_updated_at = $content->updated_at;
                }
                  $content->updated_at = date('Y-m-d h:i:s');
                  $content->save();
            }
        }
        if (!empty($changedAttributes)  && !empty($collegeID)) {
            // Existing record updated
         
            $collegeContent = CollegeContent::find()->select(['updated_at','id','content_updated_at'])
            ->where(['entity_id'=>$collegeID])
            ->andWhere(['in', 'sub_page', ['info', 'courses-fees']])
            ->all();
           
            foreach ($collegeContent as $content) {
                $content->scenario = CollegeContent::SCENARIO_IMPORTER;
                if (empty($content->content_updated_at)) {
                    $content->content_updated_at = $content->updated_at;
                }
                $content->updated_at = date('Y-m-d h:i:s');
                $content->save();
            }
        }
          
            
       
            return parent::afterSave($insert, $changedAttributes);
    }
}
