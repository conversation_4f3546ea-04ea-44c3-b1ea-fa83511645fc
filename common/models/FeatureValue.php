<?php

namespace common\models;

use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "feature_value".
 *
 * @property int $id
 * @property int $feature_id
 * @property string|null $value
 * @property string|null $unit
 * @property int|null $status
 * @property string|null $desription
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property Feature $feature
 */
class FeatureValue extends \yii\db\ActiveRecord
{
    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'feature_value';
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['feature_id'], 'required'],
            ['value', 'required', 'when' => function ($model) {
                return $model->status == self::STATUS_ACTIVE;
            }],
            [['feature_id', 'status'], 'integer'],
            [['desription'], 'string'],
            [['created_at', 'updated_at'], 'safe'],
            [['value', 'unit'], 'string', 'max' => 255],
            [['feature_id'], 'exist', 'skipOnError' => true, 'targetClass' => Feature::className(), 'targetAttribute' => ['feature_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'feature_id' => 'Feature ID',
            'value' => 'Value',
            'unit' => 'Unit',
            'status' => 'Status',
            'desription' => 'Desription',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[Feature]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\FeatureQuery
     */
    public function getFeature()
    {
        return $this->hasOne(Feature::className(), ['id' => 'feature_id']);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\FeatureValueQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\FeatureValueQuery(get_called_class());
    }


    public function afterSave($insert, $changedAttributes)
    {
          $collegeID = Yii::$app->request->get('college-id');
        if (!empty($insert) && !empty($collegeID)) {
            // New record inserted
            $collegeContent = CollegeContent::find()->select(['updated_at','id','content_updated_at'])
                       ->where(['entity_id'=>$collegeID])
                       ->andWhere(['in', 'sub_page', ['info', 'courses-fees']])
                       ->all();
            foreach ($collegeContent as $content) {
                  $content->scenario = CollegeContent::SCENARIO_IMPORTER;
                if (empty($content->content_updated_at)) {
                    $content->content_updated_at = $content->updated_at;
                }
                $content->updated_at = date('Y-m-d h:i:s');
                  $content->save();
            }
        } if (!empty($changedAttributes)  && !empty($collegeID)) {
            // Existing record updated
            $collegeContent = CollegeContent::find()->select(['updated_at','id','content_updated_at'])
            ->where(['entity_id'=>$collegeID])
            ->andWhere(['in', 'sub_page', ['info', 'courses-fees']])
            ->all();
            foreach ($collegeContent as $content) {
                $content->scenario = CollegeContent::SCENARIO_IMPORTER;
                if (empty($content->content_updated_at)) {
                    $content->content_updated_at = $content->updated_at;
                }
                $content->updated_at = date('Y-m-d h:i:s');
                $content->save();
            }
        }
          
            
       
            return parent::afterSave($insert, $changedAttributes);
    }
}
