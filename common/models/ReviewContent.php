<?php

namespace common\models;

use common\event\ReviewFilterEvent;
use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "review_content".
 *
 * @property int $id
 * @property int|null $review_id
 * @property int|null $review_category_id
 * @property string|null $content
 * @property int|null $rating
 * @property int $status
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property ReviewCategory $reviewCategory
 * @property Review $review
 */
class ReviewContent extends \yii\db\ActiveRecord
{
    const STATUS_PENDING = 0;
    const STATUS_APPROVED = 1;
    const STATUS_REJECTED = 2;

    //uncomment after importer
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            // 'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'review_content';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['review_id', 'review_category_id', 'status'], 'integer'],
            [['content'], 'string'],
            [['created_at', 'updated_at', 'rating'], 'safe'],
            [['review_category_id'], 'exist', 'skipOnError' => true, 'targetClass' => ReviewCategory::className(), 'targetAttribute' => ['review_category_id' => 'id']],
            [['review_id'], 'exist', 'skipOnError' => true, 'targetClass' => Review::className(), 'targetAttribute' => ['review_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'review_id' => 'Review ID',
            'review_category_id' => 'Review Category ID',
            'content' => 'Content',
            'rating' => 'Rating',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[ReviewCategory]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\ReviewCategoryQuery
     */
    public function getReviewCategory()
    {
        return $this->hasOne(ReviewCategory::className(), ['id' => 'review_category_id']);
    }

    /**
     * Gets query for [[Review]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\ReviewQuery
     */
    public function getReview()
    {
        return $this->hasOne(Review::className(), ['id' => 'review_id']);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\ReviewContentQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\ReviewContentQuery(get_called_class());
    }

    // //uncommnet after importer
    public function afterSave($insert, $changedAttributes)
    {
       //(new ReviewFilterEvent())->updateReviewOverallRating($this);
        if ($insert && !empty($this->review->college->id)) {
            $collegeContent = CollegeContent::find()->select(['updated_at','id','content_updated_at'])
            ->where(['entity_id'=>$this->review->college->id])
           // ->andWhere(['in', 'sub_page', ['info','reviews']])
            ->all();
            foreach ($collegeContent as $content) {
                $content->scenario = CollegeContent::SCENARIO_IMPORTER;
                if (empty($content->content_updated_at)) {
                    $content->content_updated_at = $content->updated_at;
                }
                $content->updated_at = date('Y-m-d h:i:s');
                $content->save();
            }
        }

        if (!empty($changedAttributes)  && !empty($this->review->college->id)) {
            // Existing record updated
            
            $collegeContent = CollegeContent::find()->select(['updated_at','id','content_updated_at'])
            ->where(['entity_id'=>$this->review->college->id])
           // ->andWhere(['in', 'sub_page', ['info','reviews']])
            ->all();
            foreach ($collegeContent as $content) {
                $content->scenario = CollegeContent::SCENARIO_IMPORTER;
                if (empty($content->content_updated_at)) {
                    $content->content_updated_at = $content->updated_at;
                }
                $content->updated_at = date('Y-m-d h:i:s');
                $content->save();
            }
        }
          
        return parent::afterSave($insert, $changedAttributes);
    }
}
