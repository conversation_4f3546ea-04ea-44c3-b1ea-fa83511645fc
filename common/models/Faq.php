<?php

namespace common\models;

use Yii;
use yii\base\DynamicModel;
use yii\behaviors\TimestampBehavior;
use yii\db\Expression;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

/**
 * This is the model class for table "faq".
 *
 * @property int $id
 * @property int|null $old_id
 * @property string|null $entity
 * @property int|null $entity_id
 * @property int|null $category_id
 * @property string|null $page
 * @property string|null $sub_page
 * @property string|null $child_sub_page
 * @property string|null $qnas
 * @property int|null $status
 * @property string|null $created_at
 * @property string|null $updated_at
 */
class Faq extends \yii\db\ActiveRecord
{

    const ENTITY_FAQ = 'faq';
    public $pageName;
    public $subPage;

    public static $fields = [
        'article' => ['id', 'title'],
        'exam' => ['id', 'name'],
        'board' => ['id', 'display_name'],
        'college' => ['id', 'name'],
        'filter' => ['id', 'name'],
        'course' => ['id', 'name'],
        'career' => ['id', 'name'],
        'ncert' => ['id', 'title'],
    ];

    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'faq';
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => 'updated_at',
                'value' => new Expression('NOW()'),
            ]
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['old_id', 'category_id', 'status'], 'integer'],
            // [['qnas'], 'validateQnas'],
            [['created_at', 'updated_at'], 'safe'],
            [['entity'], 'string', 'max' => 32],
            [['entity'], 'required'],
            // [['entity_id'], 'required',  'whenClient' => 'function(attribute,value){return ($("#entity").val() != "filter" )}', 'when' => function ($model) {
            //     return $model->entity != 'filter';
            // }],
            [['entity_id'], 'required'],
            [['page', 'sub_page', 'child_sub_page'], 'string', 'max' => 255],
            [['entity'], 'unique', 'targetAttribute' => ['entity', 'sub_page', 'page', 'child_sub_page'], 'when' => function ($model, $attribute) {
                return $model->isNewRecord;
            }],
            ['qnas', 'validateContentNotEmpty'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'old_id' => 'Old ID',
            'entity' => 'Type',
            'entity_id' => 'Page',
            'category_id' => 'Category ID',
            'page' => 'Page Slug',
            'sub_page' => 'Sub Page',
            'child_sub_page' => 'Dropdown Sub Page',
            'qnas' => 'Questions & Answers',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\FaqQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\FaqQuery(get_called_class());
    }

    /**
     * Gets query for [[Article]].
     *
     * @return \yii\db\ActiveQuery|ArticleQuery
     */
    public function getArticle()
    {
        return $this->hasOne(Article::className(), ['id' => 'entity_id']);
    }

    /**
     * Gets query for [[NcertArticles]].
     *
     * @return \yii\db\ActiveQuery|NcertArticlesQuery
     */
    public function getNcert()
    {
        return $this->hasOne(NcertArticles::className(), ['id' => 'entity_id']);
    }


    /**
     * Gets query for [[Exam]].
     *
     * @return \yii\db\ActiveQuery|ExamQuery
     */
    public function getExam()
    {
        return $this->hasOne(Exam::className(), ['id' => 'entity_id']);
    }

    /**
     * Gets query for [[College]].
     *
     * @return \yii\db\ActiveQuery|CollegeQuery
     */
    public function getCollege()
    {
        return $this->hasOne(College::className(), ['id' => 'entity_id']);
    }

    /**
     * Gets query for [[Board]].
     *
     * @return \yii\db\ActiveQuery|BoardQuery
     */
    public function getBoard()
    {
        return $this->hasOne(Board::className(), ['id' => 'entity_id']);
    }

    /**
     * Gets query for [[Course]].
     *
     * @return \yii\db\ActiveQuery|CollegeQuery
     */
    public function getCourse()
    {
        return $this->hasOne(Course::className(), ['id' => 'entity_id']);
    }

    /**
     * Gets query for [[Career]].
     *
     * @return \yii\db\ActiveQuery|CareerQuery
     */
    public function getCareer()
    {
        return $this->hasOne(Career::className(), ['id' => 'entity_id']);
    }


    /**
     * FAQ QNA validation.
     *
     * @param $attribute
     */
    public function validateQnas($attribute)
    {
        $items = $this->$attribute;

        if (!is_array($items)) {
            $items = [];
        }

        $dynamicModel = new DynamicModel(['question', 'answer']);
        $dynamicModel->addRule(['question', 'answer'], 'required');

        foreach ($items as $index => $item) {
            $dynamicModel->setAttributes($item);

            if (!$dynamicModel->validate()) {
                $erros = $dynamicModel->getErrors();
                foreach ($erros as $att => $error) {
                    $key = $attribute . '[' . $index . '][' . $att . ']';
                    $this->addError($key, $error['0'] ?? 'Required field, cannot be blank.');
                }
            }
        }
    }

    public function beforeSave($insert)
    {
       
        if (is_array($this->qnas)) {
            $this->qnas = Json::decode(Json::encode(array_values($this->qnas)), true);
        }

        $this->qnas = json_encode($this->qnas);
        return parent::beforeSave($insert);
    }

    public function afterFind()
    {
        $this->qnas =  ArrayHelper::toArray(json_decode($this->qnas));
        return parent::afterFind();
    }

    public function getTitle()
    {
        $entity = $this->entity;
        if (strtolower($entity) == 'filter') {
            return $this->page;
        }
        if ($this->entity == 'articles') {
            $entity = 'article';
        }
        list($id, $name) = self::$fields[$entity];

        return !empty($this->$entity->$name) ? $this->$entity->$name : '';
    }

    public function validateContentNotEmpty($attribute, $params)
    {
        foreach ($this->$attribute as $index => $item) {
            // Check if TinyMCE content is empty
            if (empty(strip_tags($item['answer'])) || empty($item['question'])) {
                $this->addError($attribute, 'Question or Answer cannot be empty.');
                break;
            }
        }
    }

    public function afterSave($insert, $changedAttributes)
    {
        $isUpdate = 0;
        if (!empty($insert)) {
            $isUpdate=1;
        }
        if (!empty($changedAttributes)) {
            $isUpdate=1;
        }
        if ($this->entity=='college' && $isUpdate==1) {
            $collegeContent = CollegeContent::find()->select(['updated_at','id','content_updated_at'])->where(['entity_id'=>$this->entity_id])->andWhere(['sub_page'=>$this->sub_page])->one();
            $collegeContent->scenario = CollegeContent::SCENARIO_IMPORTER;
            if (empty($collegeContent->content_updated_at)) {
                $collegeContent->content_updated_at = $collegeContent->updated_at;
            }
            $collegeContent->updated_at = date('Y-m-d h:i:s');
            $collegeContent->save();
        }
        return parent::afterSave($insert, $changedAttributes);
    }
}
