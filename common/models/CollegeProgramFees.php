<?php

namespace common\models;

use Yii;
use common\event\CollegeFilterEvent;
use common\event\SitemapEvent;
use common\services\CollegeService;
use yii\web\NotFoundHttpException;

/**
 * This is the model class for table "college_program_fees".
 *
 * @property int $id
 * @property int|null $college_program_id
 * @property string|null $type
 * @property int|null $fees
 * @property int|null $duration
 * @property int|null $duration_type
 * @property string|null $fees_content
 * @property int|null $status
 *
 * @property CollegeProgram $collegeProgram
 */
class CollegeProgramFees extends \yii\db\ActiveRecord
{
    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;

    const FEE_ONE_TIME = 2;


    public $college_name;

    public $program_name;


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'college_program_fees';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['college_program_id', 'fees', 'duration', 'status'], 'integer'],
            [['college_program_id', 'fees', 'duration', 'duration_type','fees_content'], 'safe'],
            [['type'], 'string', 'max' => 255],
            [['college_program_id'], 'exist', 'skipOnError' => true, 'targetClass' => CollegeProgram::className(), 'targetAttribute' => ['college_program_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'college_program_id' => 'College Program ID',
            'type' => 'Type',
            'fees' => 'Fees',
            'duration' => 'Duration',
            'duration_type' => 'Duration Type',
            'fees_content' => 'Fees Content',
            'status' => 'Status',
        ];
    }

    /**
     * Gets query for [[CollegeProgram]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CollegeProgramQuery
     */
    public function getCollegeProgram()
    {
        return $this->hasOne(CollegeProgram::className(), ['id' => 'college_program_id']);
    }

     /**
     * Gets query for [[College]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CollegeQuery
     */
    public function getCollege()
    {
        return $this->hasOne(College::className(), ['id' => 'college_id'])->viaTable('college_program', ['id' => 'college_program_id']);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\CollegeProgramFeesQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\CollegeProgramFeesQuery(get_called_class());
    }

      /**
     * Finds the CollegeProgram model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return CollegeProgram the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findCollegeProgramModel($id)
    {
        if (($model = CollegeProgram::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function afterSave($insert, $changedAttributes)
    {
        if (isset($changedAttributes['college_program_id']) && !empty($changedAttributes['college_program_id'])) {
            $collegeProgram = CollegeProgram::find()->where(['id' => $changedAttributes['college_program_id']])->one();
            // update college course collections
            CollegeService::updateProgramDocument($collegeProgram);
            CollegeService::updateCourseAvgFee($collegeProgram);

            //update filter
            (new CollegeFilterEvent())->updateCollegeFilter($collegeProgram->college);
            (new SitemapEvent())->updateCollegeProgramSitemap($collegeProgram, $collegeProgram->college->slug, $collegeProgram->college->status);
            if ($insert) {
                // New record inserted
                $collegeContent = CollegeContent::find()->select(['updated_at','id','content_updated_at'])
                              ->where(['entity_id'=>$collegeProgram->college->id])
                              ->andWhere(['in', 'sub_page', ['info', 'courses-fees']])
                              ->all();
                foreach ($collegeContent as $content) {
                    $content->scenario = CollegeContent::SCENARIO_IMPORTER;
                    if (empty($content->content_updated_at)) {
                        $content->content_updated_at = $content->updated_at;
                    }
                    $content->updated_at = date('Y-m-d h:i:s');
                    $content->save();
                }
            } if ($changedAttributes) {
                // Existing record updated
                $collegeContent = CollegeContent::find()->select(['updated_at','id','content_updated_at'])
                ->where(['entity_id'=>$collegeProgram->college->id])
                ->andWhere(['in', 'sub_page', ['info', 'courses-fees']])
                ->all();
                foreach ($collegeContent as $content) {
                    $content->scenario = CollegeContent::SCENARIO_IMPORTER;
                    if (empty($content->content_updated_at)) {
                        $content->content_updated_at = $content->updated_at;
                    }
                    $content->updated_at = date('Y-m-d h:i:s');
                    $content->save();
                }
            }
            
       
            return parent::afterSave($insert, $changedAttributes);
        }
    }
}
