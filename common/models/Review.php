<?php

namespace common\models;

use common\event\ReviewFilterEvent;
use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;
use yii\db\Query;

/**
 * This is the model class for table "review".
 *
 * @property int $id
 * @property string|null $business_unit_id
 * @property int|null $student_id
 * @property int|null $course_id
 * @property int|null $college_id
 * @property int|null $admission_year
 * @property string|null $referred_by
 * @property string|null $utm_source
 * @property string|null $utm_medium
 * @property string|null $utm_campaign
 * @property string|null $title
 * @property int|null $is_paid
 * @property int $status
 * @property int|null $terms_agreed
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property College $college
 * @property Course $course
 * @property User $user
 * @property ReviewContent[] $reviewContents
 * @property ReviewImage[] $reviewImages
 * @property ReviewQuestion[] $reviewQuestions
 */
class Review extends \yii\db\ActiveRecord
{
    const STATUS_PENDING = 0;
    const STATUS_REJECTED = 2;
    const STATUS_APPROVED = 1;

    const IS_PAID_NO = 0;
    const IS_PAID_YES = 1;

    const TERMS_AGREED_YES = 1;
    const TERMS_AGREED_NO = 0;

    const USER_BACKEND = 'admin-user';
    const USER_FRONTEND = 'frontend-user';

    const BU_GETMYUNI = 0;
    const BU_CDEKHO = 1;

    const ENTITY_REVIEW = 'review';

    const SCENARIO_IMPORTER = 'importer';

    //uncomment after importer
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            // 'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'review';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['student_id', 'college_id', 'admission_year', 'course_id'], 'required', 'except' => self::SCENARIO_IMPORTER],
            [['student_id', 'course_id', 'college_id', 'admission_year', 'is_paid', 'status', 'terms_agreed', 'business_unit_id'], 'integer'],
            [['created_at', 'updated_at', 'college_fees', 'college_fees_type'], 'safe'],
            [['referred_by', 'utm_source', 'utm_medium', 'utm_campaign', 'title'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'business_unit_id' => 'Business Unit',
            'student_id' => 'User ID',
            'course_id' => 'Course ID',
            'college_id' => 'College ID',
            'admission_year' => 'Admission Year',
            'college_fees' => 'College Fees',
            'college_fees_type' => 'College Fee Type',
            'referred_by' => 'Refered By',
            'utm_source' => 'Utm Source',
            'utm_medium' => 'Utm Medium',
            'utm_campaign' => 'Utm Campaign',
            'title' => 'Title',
            'is_paid' => 'Is Paid',
            'status' => 'Status',
            'terms_agreed' => 'Terms Agreed',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * Gets query for [[College]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CollegeQuery
     */
    public function getCollege()
    {
        return $this->hasOne(College::className(), ['id' => 'college_id']);
    }

    /**
     * Gets query for [[Course]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CourseQuery
     */
    public function getCourse()
    {
        return $this->hasOne(Course::className(), ['id' => 'course_id']);
    }

    /**
     * Gets query for [[User]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\UserQuery
     */
    public function getUser()
    {
        return $this->hasOne(Student::className(), ['id' => 'student_id']);
    }

    /**
     * Gets query for [[ReviewContents]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\ReviewContentQuery
     */
    public function getReviewContents()
    {
        return $this->hasMany(ReviewContent::className(), ['review_id' => 'id']);
    }

    /**
     * Gets query for [[ReviewImages]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\ReviewImageQuery
     */
    public function getReviewImages()
    {
        return $this->hasMany(ReviewImage::className(), ['review_id' => 'id']);
    }

    /**
     * Gets query for [[ReviewQuestions]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\ReviewQuestionQuery
     */
    public function getReviewQuestions()
    {
        return $this->hasMany(ReviewQuestion::className(), ['review_id' => 'id']);
    }

    /**
     * Gets query for [[ReviewAnswer]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\ReviewAnswerQuery
     */
    public function getReviewAnswers()
    {
        return $this->hasMany(ReviewAnswer::className(), ['review_id' => 'id']);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\ReviewQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\ReviewQuery(get_called_class());
    }

    /**
     * review admisison year
     * @return array of years from current year
     */
    public function getYearsList()
    {
        $yearFrom = date('Y-m-d', strtotime('-20 years'));
        $currentYear = date('Y');
        $yearsRange = range($currentYear, $yearFrom);

        return array_combine($yearsRange, $yearsRange);
    }

    public function getCollegeId()
    {
        $colleges = new Query();
        $colleges->select(['id', 'name'])
            ->from('college');

        $items = [];
        foreach ($colleges->batch() as $college) {
            foreach ($college as $value) {
                $items[] = [
                    'id' => $value['id'],
                    'text' => $value['name'],
                ];
            }
        }
        return $items ?? [];
    }


    // //uncommnet after importer
    public function afterSave($insert, $changedAttributes)
    {
        if ($insert) {
            $data = Review::find()->where(['id' => $this->id])->one();
            if ($data) {
                $data->slug = $data->user->id . '-' . str_replace(' ', '-', strtolower($data->user->name)) . '-review-on-' . $data->college->slug;
                $data->save();
            }
            $collegeContent = CollegeContent::find()->select(['updated_at','id','content_updated_at'])
            ->where(['entity_id'=>$data->college->id])
           // ->andWhere(['in', 'sub_page', ['info','reviews']])
            ->all();
            foreach ($collegeContent as $content) {
                $content->scenario = CollegeContent::SCENARIO_IMPORTER;
                if (empty($content->content_updated_at)) {
                    $content->content_updated_at = $content->updated_at;
                }
                $content->updated_at = date('Y-m-d h:i:s');
                $content->save();
            }
        }

        (new ReviewFilterEvent())->updateReviewElastic($this);
        if (!empty($changedAttributes)  && !empty($data->college->id)) {
            // Existing record updated
            $collegeContent = CollegeContent::find()->select(['updated_at','id','content_updated_at'])
            ->where(['entity_id'=>$data->college->id])
            //->andWhere(['in', 'sub_page', ['info','reviews']])
            ->all();
            foreach ($collegeContent as $content) {
                $content->scenario = CollegeContent::SCENARIO_IMPORTER;
                if (empty($content->content_updated_at)) {
                    $content->content_updated_at = $content->updated_at;
                }
                $content->updated_at = date('Y-m-d h:i:s');
                $content->save();
            }
        }
          
        return parent::afterSave($insert, $changedAttributes);
    }
}
