<?php

namespace common\models;

use common\event\CollegeFilterEvent;
use common\event\SitemapEvent;
use common\event\SitemapEventNew;
use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveRecord;
use common\services\CacheClearService;
use common\services\UserService;

/**
 * This is the model class for table "college_content".
 *
 * @property int $id
 * @property int|null $category_id
 * @property int $author_id
 * @property string|null $entity
 * @property int|null $entity_id
 * @property string|null $sub_page
 * @property string|null $meta_title
 * @property string|null $meta_description
 * @property string|null $h1
 * @property string|null $content
 * @property int|null $status
 * @property string|null $created_at
 * @property string|null $updated_at
 *
 * @property User $author
 */
class CollegeContent extends \yii\db\ActiveRecord
{
    const IS_MAIN_NO = 0;
    const IS_MAIN_YES = 1;
    const STATUS_INACTIVE = 0;
    const STATUS_ACTIVE = 1;
    const STATUS_DRAFT = 2;
    const SCENARIO_IMPORTER = 'importer';

    public $preview;
    public $skipAfterSave = false;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'college_content';
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'attributes' => [
                    ActiveRecord::EVENT_BEFORE_INSERT => ['created_at', 'updated_at'],
                    ActiveRecord::EVENT_BEFORE_UPDATE => 'updated_at',
                ],
                'value' => new \yii\db\Expression('NOW()'),
            ],
            //'bedezign\yii2\audit\AuditTrailBehavior'
        ];
    }

    public static function getIsMainList()
    {
        return [
            self::IS_MAIN_NO => 'No',
            self::IS_MAIN_YES => 'Yes',
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['category_id', 'author_id', 'entity_id', 'status', 'parent_id', 'template_id'], 'integer'],
            [['is_main'], 'integer'],
            [['author_id', 'entity_id', 'entity'], 'required', 'except' => self::SCENARIO_IMPORTER],
            [['content', 'sub_page', 'meta_description', 'meta_title', 'h1', 'editor_remark'], 'string'],
            [['created_at', 'updated_at','content_updated_at'], 'safe'],
            [['entity'], 'string', 'max' => 32],
            [['author_id'], 'exist', 'skipOnError' => true, 'targetClass' => User::className(), 'targetAttribute' => ['author_id' => 'id']],
        ];
    }
    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'category_id' => 'Category ID',
            'author_id' => 'Author',
            'parent_id' => 'Child',
            'entity' => 'Category',
            'entity_id' => 'Category List',
            'sub_page' => 'Sub Page',
            'meta_title' => 'Meta Title',
            'meta_description' => 'Meta Description',
            'h1' => 'H1',
            'content' => 'Content',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'is_main' => 'Is Main',
            'content_updated_at'=> 'Content Updated At',
        ];
    }

    /**
     * Gets query for [[Author]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\UserQuery
     */
    public function getAuthor()
    {
        return $this->hasOne(User::className(), ['id' => 'author_id'])->where(['not', ['user.status' => User::STATUS_DELETED]]);
    }

    public function getDefaultuser()
    {
        return UserService::getDefaultUserData();
    }

    /**
     * Gets query for [[Author]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\UserQuery
     */
    public function getBackendauthor()
    {
        return $this->hasOne(User::className(), ['id' => 'author_id']);
    }

    /**
     * Gets query for [[College]].
     *
     * @return \yii\db\ActiveQuery|\common\models\query\CollegeQuery
     */
    public function getCollege()
    {
        return $this->hasOne(College::className(), ['id' => 'entity_id']);
    }

    /**
     * Gets Entity Name for given Entity ID.
     *
     * @return /selected $entity slug attribute and value.
     */
    // public function getEntityName($entity, $id)
    // {
    //     $model = '\common\models\\' . $entity;
    //     $entityName = $model::find()->select(['slug'])->where(['id' => $id])->one();

    //     return $entityName;
    // }
    public function getEntityName($entity = null, $id = null)
    {
        $model = '\common\models\\' . ucfirst($this->entity);

        return $this->hasOne($model::className(), ['id' => 'entity_id']);
    }

    /**
     * {@inheritdoc}
     * @return \common\models\query\CollegeContentQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new \common\models\query\CollegeContentQuery(get_called_class());
    }

    // // update sitemap collections
    public function afterSave($insert, $changedAttributes)
    {
        // (new CollegeFilterEvent())->updateCollegeFilterSubPage($this->college);

        (new SitemapEventNew())->generateCollegeSitemap($this->entity_id, $this->sub_page);

        // CacheClearService::entityContent(College::ENTITY_COLLEGE, $this->parent_id, $this->sub_page, $this->college->slug, $this->college->id);
        if ($this->skipAfterSave) {
            return;
        }

        // (new SitemapEvent())->updateCollegeUpdateXml($this->entity_id, $this->status, $this->sub_page);

        return parent::afterSave($insert, $changedAttributes);
    }

    public function getTemplateName()
    {
        return $this->hasOne(ContentTemplate::className(), ['id' => 'template_id']);
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }

        $tinymceAttributes = ['description', 'content'];

        foreach ($tinymceAttributes as $attr) {
            if (!empty($this->$attr)) {
                // remove span inside h2 tag
                $this->$attr = preg_replace_callback(
                    '/<h2([^>]*)>\s*(<span[^>]*>(.*?)<\/span>)\s*<\/h2>/is',
                    function ($matches) {
                        return "<h2{$matches[1]}>" . trim($matches[3]) . '</h2>';
                    },
                    $this->$attr
                );

                // wrap with main tag
                $cleanContent = trim($this->$attr);
                $cleanContent = preg_replace('/<\/?(html|body)[^>]*>/i', '', $cleanContent);
                if ($this->is_main) {
                    if (!preg_match('/<main[^>]*>.*<\/main>/is', $cleanContent)) {
                        $cleanContent = "<main>\n" . $cleanContent . "\n</main>";
                    }
                } else {
                    $cleanContent = preg_replace('/<\/?main[^>]*>/i', '', $cleanContent);
                }
                $this->$attr = $cleanContent;
            }
        }

        return true;
    }
}
